#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
消防违法行为JSON数据验证脚本
验证text.json文件的结构和内容完整性
"""

import json
import sys

def validate_json_structure():
    """验证JSON文件结构和内容"""
    try:
        # 读取JSON文件
        with open('text.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print("✅ JSON格式验证成功！")
        print(f"📊 数据块数量: {len(data)}")
        
        # 验证每个数据块的结构
        required_fields = [
            '编号', '违法行为', '处罚种类及幅度', '违法主体', 
            '违反条款', '处罚依据', '实施主体', '违法情形划分'
        ]
        
        for i, item in enumerate(data):
            print(f"\n🔍 验证数据块 {i+1}: {item.get('编号', 'N/A')}")
            
            # 检查必需字段
            missing_fields = []
            for field in required_fields:
                if field not in item:
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"  ❌ 缺少字段: {', '.join(missing_fields)}")
            else:
                print(f"  ✅ 所有必需字段完整")
            
            # 检查违法情形划分
            if '违法情形划分' in item:
                violation_levels = item['违法情形划分']
                levels = ['严重违法', '一般违法', '较轻违法']
                
                for level in levels:
                    if level in violation_levels:
                        print(f"    ✅ 包含 {level} 情形")
                    else:
                        print(f"    ⚠️  缺少 {level} 情形")
        
        # 统计信息
        print(f"\n📈 统计信息:")
        print(f"  - 总数据块数: {len(data)}")
        print(f"  - 编号范围: {data[0].get('编号')} 到 {data[-1].get('编号')}")
        
        # 检查违法主体类型
        subjects = set()
        for item in data:
            if '违法主体' in item:
                subjects.add(item['违法主体'])
        print(f"  - 违法主体类型: {', '.join(subjects)}")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ JSON格式错误: {e}")
        return False
    except FileNotFoundError:
        print("❌ 文件 text.json 不存在")
        return False
    except Exception as e:
        print(f"❌ 验证过程中出现错误: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始验证消防违法行为JSON数据...")
    success = validate_json_structure()
    
    if success:
        print("\n🎉 验证完成！JSON文件结构正确，数据完整。")
        sys.exit(0)
    else:
        print("\n💥 验证失败！请检查JSON文件。")
        sys.exit(1)
