#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证新增的JSON文件（01-7到02-5）的格式和内容完整性
"""

import json
import os

def validate_new_json_files():
    """验证新增的JSON文件"""
    # 新增的文件列表
    new_files = [
        '01-7.json', '01-8.json', '01-9.json', '01-10.json', '01-11.json',
        '01-12.json', '01-13.json', '01-14.json', '01-15.json', '01-16.json',
        '01-17.json', '01-18.json', '01-19.json', '01-20.json', '01-21.json',
        '02-1.json', '02-2.json', '02-3.json', '02-4.json', '02-5.json'
    ]
    
    print("🚀 开始验证新增的JSON文件...")
    print(f"📊 待验证文件数量: {len(new_files)}")
    
    all_valid = True
    total_size = 0
    
    for filename in new_files:
        print(f"\n🔍 验证文件: {filename}")
        
        if not os.path.exists(filename):
            print(f"  ❌ 文件不存在: {filename}")
            all_valid = False
            continue
        
        try:
            # 读取并验证JSON格式
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"  ✅ JSON格式正确")
            
            # 验证必需字段
            required_fields = [
                '编号', '违法行为', '处罚种类及幅度', '违法主体', 
                '违反条款', '处罚依据', '实施主体'
            ]
            
            missing_fields = []
            for field in required_fields:
                if field not in data:
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"  ❌ 缺少字段: {', '.join(missing_fields)}")
                all_valid = False
            else:
                print(f"  ✅ 所有必需字段完整")
            
            # 检查数据结构类型
            if '违法情形划分' in data:
                print(f"    ✅ 包含违法情形划分结构")
            elif '处罚阶次划分' in data:
                print(f"    ✅ 包含处罚阶次划分结构")
            else:
                print(f"    ⚠️  未找到标准的划分结构")
            
            # 显示基本信息
            print(f"  📋 编号: {data.get('编号', 'N/A')}")
            print(f"  📋 违法行为: {data.get('违法行为', 'N/A')[:50]}...")
            print(f"  📋 违法主体: {data.get('违法主体', 'N/A')}")
            
            # 检查文件大小
            file_size = os.path.getsize(filename)
            total_size += file_size
            print(f"  📊 文件大小: {file_size:,} 字节")
            
        except json.JSONDecodeError as e:
            print(f"  ❌ JSON格式错误: {e}")
            all_valid = False
        except Exception as e:
            print(f"  ❌ 验证过程中出现错误: {e}")
            all_valid = False
    
    # 总结
    print(f"\n📈 验证总结:")
    print(f"  - 验证文件数: {len(new_files)}")
    print(f"  - 总文件大小: {total_size:,} 字节 ({total_size/1024:.1f} KB)")
    print(f"  - 平均文件大小: {total_size//len(new_files):,} 字节")
    print(f"  - 验证状态: {'✅ 全部通过' if all_valid else '❌ 存在问题'}")
    
    # 按系列分类统计
    series_01 = [f for f in new_files if f.startswith('01-')]
    series_02 = [f for f in new_files if f.startswith('02-')]
    
    print(f"\n📊 按系列分类:")
    print(f"  - 01系列: {len(series_01)}个文件 (01-7 到 01-21)")
    print(f"  - 02系列: {len(series_02)}个文件 (02-1 到 02-5)")
    
    if all_valid:
        print(f"\n🎉 所有新增JSON文件验证成功！")
        print(f"✅ 格式正确，数据完整")
        print(f"✅ 可用于消防执法系统")
        print(f"✅ 与之前的文件保持结构一致性")
    else:
        print(f"\n💥 部分JSON文件验证失败！")
        print(f"❌ 请检查上述错误信息")
    
    return all_valid

if __name__ == "__main__":
    validate_new_json_files()
