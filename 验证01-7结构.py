#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门验证01-7数据结构完整性的脚本
检查01-7是否与01-6保持一致的完整结构
"""

import json

def validate_01_7_structure():
    """验证01-7数据结构完整性"""
    try:
        # 读取JSON文件
        with open('text.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 找到01-6和01-7数据块
        item_01_6 = None
        item_01_7 = None
        
        for item in data:
            if item.get('编号') == '01-6':
                item_01_6 = item
            elif item.get('编号') == '01-7':
                item_01_7 = item
        
        if not item_01_6 or not item_01_7:
            print("❌ 未找到01-6或01-7数据块")
            return False
        
        print("🔍 验证01-7数据结构完整性...")
        print(f"📋 01-7违法行为: {item_01_7.get('违法行为', 'N/A')}")
        
        # 检查违法情形划分
        violation_levels = item_01_7.get('违法情形划分', {})
        required_levels = ['严重违法', '一般违法', '较轻违法']
        
        for level in required_levels:
            if level not in violation_levels:
                print(f"❌ 缺少 {level} 级别")
                return False
            
            level_data = violation_levels[level]
            print(f"\n✅ {level}:")
            print(f"  具体违法情形: {level_data.get('具体违法情形', 'N/A')}")
            print(f"  个人量罚区间: {level_data.get('个人量罚区间', 'N/A')}")
            
            # 检查单位场所类别
            if '单位' in level_data:
                unit_data = level_data['单位']
                if '场所类别' in unit_data:
                    place_categories = unit_data['场所类别']
                    required_categories = [
                        '属于或者含有公共娱乐场所的单位、场所',
                        '属于或者含有公共娱乐场所外的人员密集的单位、场所',
                        '不设有人员密集场所的单位、场所',
                        '地下单体建筑；生产、储存、装卸易燃易爆危险物品的工厂、仓库和专用车站、码头，易燃易爆气体和液体的充装站、供应站、调压站'
                    ]
                    
                    for category in required_categories:
                        if category in place_categories:
                            area_data = place_categories[category]
                            area_key = list(area_data.keys())[0]  # 获取面积字段名
                            area_ranges = area_data[area_key]
                            print(f"    ✅ {category[:20]}... ({len(area_ranges)}个面积区间)")
                        else:
                            print(f"    ❌ 缺少场所类别: {category[:30]}...")
                            return False
                else:
                    print(f"    ❌ {level} 缺少场所类别数据")
                    return False
            else:
                print(f"    ❌ {level} 缺少单位数据")
                return False
        
        # 比较01-6和01-7的结构一致性
        print(f"\n🔄 比较01-6和01-7结构一致性...")
        
        for level in required_levels:
            level_01_6 = item_01_6['违法情形划分'][level]
            level_01_7 = item_01_7['违法情形划分'][level]
            
            # 检查场所类别数量是否一致
            if '单位' in level_01_6 and '单位' in level_01_7:
                categories_01_6 = level_01_6['单位']['场所类别'].keys()
                categories_01_7 = level_01_7['单位']['场所类别'].keys()
                
                if set(categories_01_6) == set(categories_01_7):
                    print(f"  ✅ {level} 场所类别一致 ({len(categories_01_7)}个)")
                else:
                    print(f"  ❌ {level} 场所类别不一致")
                    print(f"    01-6: {len(categories_01_6)}个")
                    print(f"    01-7: {len(categories_01_7)}个")
                    return False
        
        print(f"\n🎉 01-7数据结构验证通过！")
        print(f"✅ 包含完整的三级违法情形")
        print(f"✅ 每级都包含完整的场所类别数据")
        print(f"✅ 与01-6结构保持一致")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ JSON格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 验证过程中出现错误: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始验证01-7数据结构...")
    success = validate_01_7_structure()
    
    if success:
        print("\n✅ 验证成功！01-7数据结构完整且与01-6保持一致。")
    else:
        print("\n❌ 验证失败！01-7数据结构存在问题。")
