import logging
import logging.config
from typing import TypedDict, Optional, List, Dict
import random

from langchain_community.utilities import SQLDatabase
from langchain_core.documents import Document
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langgraph.constants import END
from langgraph.graph import StateGraph
from pydantic import BaseModel, Field

from chains import llm, law_store


# 状态定义
class AgentState(TypedDict):
    input: str
    department_code: str
    laws: List[Document]
    affairs: List[Dict]
    message: str


class Law(BaseModel):
    code: str = Field(description="法律条文编码")
    content: str = Field(description="法律条文")
    administrative_matter: list[str] = Field(description="建议的行政事项")
    administrative_matter_code: list[str] = Field(description="建议的行政事项编码")


# 定义JSON响应结构
class LegalResponse(BaseModel):
    question: str = Field(description="原始用户问题")
    answer: str = Field(description="生成的咨询回答")
    legal_basis: list[Law] = Field(description="依据的法律")
    suggested_actions: list[str] = Field(description="建议的执法步骤")

def case_name(state: AgentState) -> dict[str, str]:
    return {"case_name": "test_case"}


def retrieve_laws(state: AgentState) -> dict[str, str]:
    logging.debug(f"retrieve_laws节点: {state}")
    retriever = law_store.as_retriever(search_kwargs={"k": 3})
    docs = retriever.invoke(state["input"])
    return {"laws": docs}


parser = JsonOutputParser(pydantic_object=LegalResponse)
# 生成专业回答
prompt = ChatPromptTemplate.from_template("""
    你是一名行政执法专家，请根据以下上下文回答用户问题：

    {context}

    用户问题：{input}

    请严格按照以下JSON格式回复：
    {format_instructions}

    要求：
    1、结合法律及其关联的行政事项，推荐一条最合适的法律，并从中选择一条最合适的行政事项。

""").partial(format_instructions=parser.get_format_instructions())

legal_chain = prompt | llm | JsonOutputParser(pydantic_object=LegalResponse)


def consult_laws(state: AgentState) -> dict[str, str]:
    response = legal_chain.invoke(({"input": state["input"],
                                    "context": state["laws"]
                                    }))
    return {"message": response}


def _route(state: AgentState) -> dict[str, str]:
    return {}

def _route_by_condition(state: AgentState) -> str:
    if state.get("department_code"):
        return "department_provided"
    return "department_not_provided"

def retrieve_laws_with_discretion(state: AgentState) -> dict[str, str]:
    logging.debug(f"dev 节点参数: {state}")
    retriever = law_store.as_retriever(search_kwargs={"k": 5})
    docs = retriever.invoke(state["input"])
    
    # 获取所有文档的事项列表
    appr_list = []
    for doc in docs:
        doc_appr_list = doc.metadata.get("事项列表", [])
        if doc_appr_list:
            appr_list.extend(doc_appr_list)
    
    # 去重处理，基于事项ID
    unique_appr_list = []
    seen_ids = set()
    for item in appr_list:
        item_id = item.get('id')
        if item_id and item_id not in seen_ids:
            seen_ids.add(item_id)
            unique_appr_list.append(item)
    
    appr_list = unique_appr_list
    id_list = [item['id'] for item in appr_list]
    print("id_list: ", id_list)
    
    if not id_list:
        print("未找到相关事项")
        # 降级到返回所有法律条文
        return {"laws": docs}
    
    db = SQLDatabase.from_uri("mysql+pymysql://root:minstone123@192.168.2.154:3306/law_item_prod_gd")
    
    # 构建 IN 子句的占位符
    id_placeholders = ','.join([f"'{id}'" for id in id_list])
    
    # 获取关联的事项 - 添加 DISTINCT 过滤重复
    result = db.run(f"""
        SELECT DISTINCT FOLDER_ID, DEPT_CODE, DEPT_NAME
        FROM APPR_MATTER_CLAIM
        WHERE FOLDER_ID IN ({id_placeholders})
        AND DEPT_CODE = '{state["department_code"]}'
    """)
    
    folder_ids = []
    if isinstance(result, str):
        import ast
        try:
            parsed_result = ast.literal_eval(result)
            if isinstance(parsed_result, list):
                folder_ids = [str(row[0]) for row in parsed_result if row]
        except (ValueError, SyntaxError):
            logging.error(f"解析查询结果失败: {result}")
    else:
        folder_ids = [str(row[0]) for row in result if row]

    # 测试数据
    # folder_ids = id_list
    
    if not folder_ids:
        print("未找到部门关联的事项")
        # 降级到返回所有法律条文
        return {"laws": docs}
    
    print("folder_ids: ", folder_ids)
    
    # 获取违法行为
    folder_placeholders = ','.join([f"'{id}'" for id in folder_ids])
    
    db2 = SQLDatabase.from_uri("mysql+pymysql://root:minstone123@192.168.2.154:3306/law_item_sc")
    result_item = db2.run(f"""
        SELECT DISTINCT id, task_code 
        FROM law_item_sc.appr_matter_base_info ambi 
        WHERE id IN ({folder_placeholders})
    """)
    
    # 建立 matter_id 到 task_code 的映射
    matter_to_task_map = {}
    if isinstance(result_item, str):
        import ast
        try:
            parsed_result = ast.literal_eval(result_item)
            if isinstance(parsed_result, list):
                for row in parsed_result:
                    if row and len(row) >= 2:
                        matter_to_task_map[str(row[0])] = str(row[1])  # id -> task_code
        except (ValueError, SyntaxError):
            logging.error(f"解析事项列表查询结果失败: {result_item}")
    else:
        for row in result_item:
            if row and len(row) >= 2:
                matter_to_task_map[str(row[0])] = str(row[1])  # id -> task_code
    
    task_codes = list(matter_to_task_map.values())
    print(task_codes)
    task_code_placeholders = ','.join([f"'{code}'" for code in task_codes])
    # 获取违法行为详情 - 添加 DISTINCT 过滤重复
    result_atb = db.run(f"""
        SELECT DISTINCT atb.id as id, atb.cause_name as name, atb.cause_code as code, 
               atb.illegal_law_reference as illegal_law_reference, 
               atb.punish_law_reference as punish_law_reference, 
               atbm.task_code as folder_id 
        FROM appr_task_basis atb 
        INNER JOIN appr_task_basis_matter atbm ON atbm.task_id = atb.id 
        WHERE atb.is_deleted = 0 AND atbm.task_code IN ({task_code_placeholders})
    """)
    # 查询数据库测试的数据
    # result_atb = db.run(f"""
    #     select atb.id as id, atb.cause_name as name, atb.cause_code as code, 
    #            atb.illegal_law_reference as illegal_law_reference, 
    #            atb.punish_law_reference as punish_law_reference, 
    #            atbm.task_code as folder_id 
    #     from appr_task_basis atb 
    #     inner join appr_task_basis_matter atbm on atbm.task_id = atb.id 
    #     where atb.is_deleted = 0
    #     and atb.org_code = '{state["department_code"]}' limit 10
    # """)
    
    # 解析违法行为数据
    atb_data = []
    if isinstance(result_atb, str):
        import ast
        try:
            parsed_result = ast.literal_eval(result_atb)
            if isinstance(parsed_result, list):
                atb_data = parsed_result
        except (ValueError, SyntaxError):
            logging.error(f"解析违法行为结果失败: {result_atb}")
    else:
        atb_data = result_atb
    
    # 构造测试数据 - 将tuple转换为list并随机分配folder_id
    # modified_atb_data = []
    # for row in atb_data:
    #     row_list = list(row)  # 转换tuple为list
    #     if len(row_list) >= 6:  # 确保有足够的列
    #         row_list[5] = random.choice(id_list)  # folder_id是第6列(索引5)
    #     modified_atb_data.append(tuple(row_list))  # 转回tuple

    # atb_data = modified_atb_data

    atb_ids = [str(row[0]) for row in atb_data if row]
    
    if not atb_ids:
        print("未找到相关违法行为")
        # 降级到返回所有法律条文
        return {"laws": docs}
    
    print("atb_ids: ", atb_ids)
    
    # 获取裁量标准 - 添加 DISTINCT 过滤重复
    atb_placeholders = ','.join([f"'{id}'" for id in atb_ids])
    result_ads = db.run(f"""
        SELECT DISTINCT ads.id as id, ads.disc_level as disc_level, ads.applicable_condition as applicable_condition, 
               ads.punish_standard as punish_standard, 
               adb.task_basis_id as atb_id  
        FROM appr_discretion_standard ads
        INNER JOIN appr_discretion_basis adb ON adb.id = ads.disc_basis_id 
        AND adb.task_basis_id IN ({atb_placeholders})
        WHERE ads.is_deleted = 0
    """)
    
    # 解析裁量标准数据
    ads_data = []
    if isinstance(result_ads, str):
        import ast
        try:
            parsed_result = ast.literal_eval(result_ads)
            if isinstance(parsed_result, list):
                ads_data = parsed_result
        except (ValueError, SyntaxError):
            logging.error(f"解析裁量标准结果失败: {result_ads}")
    else:
        ads_data = result_ads
    
    # 构建关联树结构
    for doc in docs:
        appr_list = []
        for matter in doc.metadata.get("事项列表", []):
            matter_id = matter['id']
            if matter_id not in id_list:
                continue
            matter_node = {
                "id": matter_id,
                "name": matter.get('name', ''),
                "businesscodes": matter.get('businesscodes', ''),
                "type": "matter",
                "违法行为": []
            }
            
            # 查找关联的违法行为
            for atb_row in atb_data:
                # 通过映射关系比较：matter_id -> task_code -> atb_row[5]
                matter_task_code = matter_to_task_map.get(str(matter_id))
                if matter_task_code and str(atb_row[5]) == matter_task_code:  # task_code 匹配
                    atb_node = {
                        "id": str(atb_row[0]),
                        "name": atb_row[1],
                        "code": atb_row[2],
                        "illegal_law_reference": atb_row[3],
                        "punish_law_reference": atb_row[4],
                        "type": "violation",
                        "裁量标准": []
                    }
                    
                    # 查找关联的裁量标准
                    for ads_row in ads_data:
                        if str(ads_row[4]) == str(atb_row[0]):  # atb_id 匹配
                            ads_node = {
                                "id": str(ads_row[0]),
                                "disc_level": ads_row[1],
                                "applicable_condition": ads_row[2],
                                "punish_standard": ads_row[3],
                                "type": "discretion"
                            }
                            atb_node["裁量标准"].append(ads_node)
                    
                    matter_node["违法行为"].append(atb_node)
            appr_list.append(matter_node)
        doc.metadata["事项列表"] = appr_list

    # return {"message": {"tree": tree, "summary": f"构建了包含{len(tree)}个事项的关联树"}}
    return {"laws": docs}

workflow = StateGraph(AgentState)
workflow.add_node("route",_route)
workflow.add_node("retrieve_laws", retrieve_laws)
workflow.add_node("retrieve_laws_with_discretion", retrieve_laws_with_discretion)
workflow.add_node("consult_laws", consult_laws)

workflow.set_entry_point("route")

# 添加条件边
workflow.add_conditional_edges(
    "route",
    _route_by_condition,
    {
        "department_not_provided": "retrieve_laws",
        "department_provided": "retrieve_laws_with_discretion",
    }
)

workflow.add_edge("retrieve_laws", "consult_laws")
workflow.add_edge("retrieve_laws_with_discretion", "consult_laws")
workflow.add_edge("consult_laws", END)
structure_legal_chain = workflow.compile()

if __name__ == "__main__":
    print(structure_legal_chain.invoke({"input": "夸大宣传","department_code":"10086"}))
