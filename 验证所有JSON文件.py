#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证所有单独JSON文件的格式和内容完整性
"""

import json
import os

def validate_json_files():
    """验证所有JSON文件"""
    json_files = ['01-1.json', '01-2.json', '01-3.json', '01-4.json', '01-5.json', '01-6.json']
    
    print("🚀 开始验证所有单独JSON文件...")
    
    all_valid = True
    
    for filename in json_files:
        print(f"\n🔍 验证文件: {filename}")
        
        if not os.path.exists(filename):
            print(f"  ❌ 文件不存在: {filename}")
            all_valid = False
            continue
        
        try:
            # 读取并验证JSON格式
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"  ✅ JSON格式正确")
            
            # 验证必需字段
            required_fields = [
                '编号', '违法行为', '处罚种类及幅度', '违法主体', 
                '违反条款', '处罚依据', '实施主体', '违法情形划分'
            ]
            
            missing_fields = []
            for field in required_fields:
                if field not in data:
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"  ❌ 缺少字段: {', '.join(missing_fields)}")
                all_valid = False
            else:
                print(f"  ✅ 所有必需字段完整")
            
            # 验证违法情形划分
            if '违法情形划分' in data:
                violation_levels = data['违法情形划分']
                levels = ['严重违法', '一般违法', '较轻违法']
                
                for level in levels:
                    if level in violation_levels:
                        print(f"    ✅ 包含 {level} 情形")
                    else:
                        print(f"    ⚠️  缺少 {level} 情形")
            
            # 显示基本信息
            print(f"  📋 编号: {data.get('编号', 'N/A')}")
            print(f"  📋 违法行为: {data.get('违法行为', 'N/A')[:50]}...")
            print(f"  📋 违法主体: {data.get('违法主体', 'N/A')}")
            
            # 检查文件大小
            file_size = os.path.getsize(filename)
            print(f"  📊 文件大小: {file_size:,} 字节")
            
        except json.JSONDecodeError as e:
            print(f"  ❌ JSON格式错误: {e}")
            all_valid = False
        except Exception as e:
            print(f"  ❌ 验证过程中出现错误: {e}")
            all_valid = False
    
    # 总结
    print(f"\n📈 验证总结:")
    print(f"  - 验证文件数: {len(json_files)}")
    print(f"  - 验证状态: {'✅ 全部通过' if all_valid else '❌ 存在问题'}")
    
    if all_valid:
        print(f"\n🎉 所有JSON文件验证成功！")
        print(f"✅ 格式正确，数据完整")
        print(f"✅ 可用于消防执法系统")
    else:
        print(f"\n💥 部分JSON文件验证失败！")
        print(f"❌ 请检查上述错误信息")
    
    return all_valid

if __name__ == "__main__":
    validate_json_files()
