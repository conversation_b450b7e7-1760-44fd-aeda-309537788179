# 消防违法行为数据结构说明文档

## 概述

本文档说明了 `text.json` 文件中消防违法行为数据的结构和内容。该JSON文件包含了5个消防违法行为的详细信息，每个违法行为都按照统一的数据结构进行组织。

## 数据结构

### 根级别结构

JSON文件的根级别是一个数组，包含5个消防违法行为对象。

### 违法行为对象结构

每个违法行为对象包含以下字段：

#### 基本信息字段

- **编号**: 违法行为的唯一标识符（如："01-1", "01-2"等）
- **违法行为**: 违法行为的具体描述
- **处罚种类及幅度**: 对应的处罚措施和罚款范围
- **违法主体**: 可能实施该违法行为的主体类型（单位、个人或两者）
- **违反条款**: 违反的具体法律条款
- **处罚依据**: 处罚的法律依据
- **实施主体**: 负责执行处罚的机构

#### 违法情形划分

每个违法行为都包含一个 `违法情形划分` 对象，该对象根据违法严重程度分为三个级别：

1. **严重违法**: 最严重的违法情形
2. **一般违法**: 中等严重程度的违法情形  
3. **较轻违法**: 相对较轻的违法情形

每个违法情形级别包含：
- **具体违法情形**: 该级别违法行为的具体描述
- **场所类别**: 不同类型场所对应的处罚标准（如适用）
- **个人量罚区间**: 针对个人的处罚金额范围（如适用）
- **单位量罚区间**: 针对单位的处罚金额范围（如适用）

## 包含的违法行为

### 01-1: 公众聚集场所未经许可擅自投入使用、营业
- **特点**: 包含详细的场所类别分类和面积对应的处罚标准
- **场所类别**: 
  - 宾馆、饭店、商场、集贸市场
  - 客运站候车室、客运码头候船厅、民用机场航站楼
  - 体育场馆、会堂
  - 公共娱乐场所

### 01-2: 场所使用、营业情况与承诺内容不符
- **特点**: 主要基于不符项目数量来划分违法严重程度
- **划分标准**: 
  - 严重违法: 5项以上不符或构成重大火灾隐患
  - 一般违法: 3-5项不符
  - 较轻违法: 3项以下不符

### 01-3: 消防设施、器材、消防安全标志问题
- **特点**: 针对单位的违法行为，处罚金额5千-5万元
- **主要关注**: 消防系统的设置、损坏情况和维护状态

### 01-4: 损坏、挪用消防设施、器材
- **特点**: 同时适用于单位和个人
- **处罚方式**: 
  - 单位: 5千-5万元罚款
  - 个人: 警告或500元以下罚款

### 01-5: 占用、堵塞、封闭疏散通道、安全出口
- **特点**: 同时适用于单位和个人
- **处罚方式**: 
  - 单位: 5千-5万元罚款  
  - 个人: 警告或500元以下罚款

## 数据特点

1. **结构化**: 所有数据都按照统一的JSON结构组织，便于程序处理
2. **层次化**: 违法情形按严重程度分级，处罚标准按场所类型和面积细分
3. **完整性**: 包含了违法行为的完整信息，从法律条款到具体处罚标准
4. **实用性**: 可直接用于消防执法系统的数据支撑

## 使用说明

该JSON文件可以用于：
- 消防执法管理系统的数据源
- 违法行为查询和处罚标准确定
- 消防法律法规的数字化应用
- 相关培训和教育材料的数据支撑

## 技术规格

- **文件格式**: JSON (JavaScript Object Notation)
- **编码**: UTF-8
- **文件大小**: 约194行
- **数据验证**: 已通过JSON格式验证，确保数据结构正确
