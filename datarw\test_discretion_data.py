"""
自由裁量权数据处理模块测试脚本
用于测试 discretion_data.py 模块的功能
"""

import os
import json
import unittest
from discretion_data import DiscretionDataProcessor


class TestDiscretionDataProcessor(unittest.TestCase):
    """自由裁量权数据处理器测试类"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.test_file = "datarw/data/4675378.docx"
        self.output_file = "datarw/data/test_output.json"
        self.processor = DiscretionDataProcessor(self.test_file)
    
    def tearDown(self):
        """测试后的清理工作"""
        if os.path.exists(self.output_file):
            os.remove(self.output_file)
    
    def test_load_document(self):
        """测试文档加载功能"""
        result = self.processor.load_document()
        self.assertTrue(result, "文档加载应该成功")
        self.assertIsNotNone(self.processor.document, "文档对象不应为空")
    
    def test_load_nonexistent_document(self):
        """测试加载不存在的文档"""
        processor = DiscretionDataProcessor("nonexistent.docx")
        result = processor.load_document()
        self.assertFalse(result, "加载不存在的文档应该失败")
    
    def test_process_all_tables(self):
        """测试表格处理功能"""
        self.processor.load_document()
        tables_data = self.processor.process_all_tables()
        
        self.assertIsInstance(tables_data, list, "返回结果应该是列表")
        self.assertGreater(len(tables_data), 0, "应该至少有一个表格")
        
        # 检查第一个表格的结构
        first_table = tables_data[0]
        self.assertIn('表格编号', first_table, "表格应该有编号")
        self.assertIn('表格行数', first_table, "表格应该有行数")
        self.assertIn('表格列数', first_table, "表格应该有列数")
        self.assertIn('数据', first_table, "表格应该有数据")
        
        # 检查数据结构
        data = first_table['数据']
        self.assertIsInstance(data, list, "数据应该是列表")
        if data:
            first_row = data[0]
            self.assertIn('行号', first_row, "每行数据应该有行号")
    
    def test_save_to_json(self):
        """测试 JSON 保存功能"""
        self.processor.load_document()
        self.processor.process_all_tables()
        
        result = self.processor.save_to_json(self.output_file)
        self.assertTrue(result, "JSON 保存应该成功")
        self.assertTrue(os.path.exists(self.output_file), "输出文件应该存在")
        
        # 验证 JSON 文件内容
        with open(self.output_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        self.assertIn('文档信息', data, "JSON 应该包含文档信息")
        self.assertIn('表格数据', data, "JSON 应该包含表格数据")
        
        doc_info = data['文档信息']
        self.assertIn('源文件', doc_info, "文档信息应该包含源文件")
        self.assertIn('表格数量', doc_info, "文档信息应该包含表格数量")
        self.assertIn('处理时间', doc_info, "文档信息应该包含处理时间")
    
    def test_get_summary(self):
        """测试摘要功能"""
        self.processor.load_document()
        self.processor.process_all_tables()

        summary = self.processor.get_summary()

        self.assertIn('文档路径', summary, "摘要应该包含文档路径")
        self.assertIn('表格总数', summary, "摘要应该包含表格总数")
        self.assertIn('数据总行数', summary, "摘要应该包含数据总行数")
        self.assertIn('表格详情', summary, "摘要应该包含表格详情")

        self.assertIsInstance(summary['表格详情'], list, "表格详情应该是列表")

    def test_group_data_by_column_name(self):
        """测试按列名分组功能"""
        self.processor.load_document()
        self.processor.process_all_tables()

        # 按"违法行为"列分组
        grouped_data = self.processor.group_data_by_column(column_name="违法行为")

        self.assertIsInstance(grouped_data, list, "分组结果应该是列表")
        self.assertGreater(len(grouped_data), 0, "应该至少有一个表格的分组数据")

        # 检查分组数据结构
        first_table = grouped_data[0]
        self.assertIn('表格编号', first_table, "分组数据应该包含表格编号")
        self.assertIn('分组列', first_table, "分组数据应该包含分组列")
        self.assertIn('分组数量', first_table, "分组数据应该包含分组数量")
        self.assertIn('分组数据', first_table, "分组数据应该包含分组数据")

        # 检查分组内容
        groups = first_table['分组数据']
        self.assertIsInstance(groups, list, "分组数据应该是列表")
        if groups:
            first_group = groups[0]
            self.assertIn('分组值', first_group, "每个分组应该有分组值")
            self.assertIn('原始行数', first_group, "每个分组应该有原始行数")
            self.assertIn('合并后数据', first_group, "每个分组应该有合并后数据")

            # 检查合并后数据结构
            merged_data = first_group['合并后数据']
            self.assertIn('违法行为', merged_data, "合并后数据应该包含分组列")

            # 如果有 list 字段，检查其结构
            if 'list' in merged_data:
                self.assertIsInstance(merged_data['list'], list, "list 字段应该是列表")
                if merged_data['list']:
                    first_item = merged_data['list'][0]
                    self.assertIsInstance(first_item, dict, "list 中的项应该是字典")

    def test_group_data_by_column_index(self):
        """测试按列索引分组功能"""
        self.processor.load_document()
        self.processor.process_all_tables()

        # 按第4列（裁量阶次）分组
        grouped_data = self.processor.group_data_by_column(column_index=4)

        self.assertIsInstance(grouped_data, list, "分组结果应该是列表")
        self.assertGreater(len(grouped_data), 0, "应该至少有一个表格的分组数据")

        # 验证分组列是正确的
        first_table = grouped_data[0]
        self.assertIn('裁量', first_table['分组列'], "分组列应该是裁量阶次相关")

    def test_save_grouped_data_to_json(self):
        """测试分组数据保存功能"""
        self.processor.load_document()
        self.processor.process_all_tables()

        # 生成分组数据
        grouped_data = self.processor.group_data_by_column(column_name="违法行为")

        # 保存分组数据
        grouped_output = "datarw/data/test_grouped_output.json"
        result = self.processor.save_grouped_data_to_json(grouped_data, grouped_output)

        self.assertTrue(result, "分组数据保存应该成功")
        self.assertTrue(os.path.exists(grouped_output), "分组数据文件应该存在")

        # 验证文件内容
        with open(grouped_output, 'r', encoding='utf-8') as f:
            data = json.load(f)

        self.assertIn('文档信息', data, "JSON 应该包含文档信息")
        self.assertIn('分组统计', data, "JSON 应该包含分组统计")
        self.assertIn('分组数据', data, "JSON 应该包含分组数据")

        # 清理测试文件
        if os.path.exists(grouped_output):
            os.remove(grouped_output)

    def test_get_grouped_summary(self):
        """测试分组摘要功能"""
        self.processor.load_document()
        self.processor.process_all_tables()

        # 生成分组数据
        grouped_data = self.processor.group_data_by_column(column_name="违法行为")

        # 获取分组摘要
        summary = self.processor.get_grouped_summary(grouped_data)

        self.assertIn('文档路径', summary, "分组摘要应该包含文档路径")
        self.assertIn('表格总数', summary, "分组摘要应该包含表格总数")
        self.assertIn('总分组数', summary, "分组摘要应该包含总分组数")
        self.assertIn('总数据行数', summary, "分组摘要应该包含总数据行数")
        self.assertIn('分组详情', summary, "分组摘要应该包含分组详情")

    def test_intelligent_merging(self):
        """测试智能合并功能"""
        self.processor.load_document()
        self.processor.process_all_tables()

        # 按"违法行为"列分组
        grouped_data = self.processor.group_data_by_column(column_name="违法行为")

        # 检查第一个分组的智能合并结果
        first_group = grouped_data[0]['分组数据'][0]
        merged_data = first_group['合并后数据']

        # 应该有公共字段（违法行为）
        self.assertIn('违法行为', merged_data, "应该包含违法行为字段")

        # 应该有 list 字段包含变化的数据
        if 'list' in merged_data:
            list_items = merged_data['list']
            self.assertGreater(len(list_items), 0, "list 应该包含至少一个项目")

            # 检查 list 中的项目是否包含变化的字段
            for item in list_items:
                # 应该包含裁量相关字段或其他有效字段
                self.assertGreater(len(item), 0, "list 项目应该包含有效字段")

    def test_invalid_column_parameters(self):
        """测试无效列参数的处理"""
        self.processor.load_document()
        self.processor.process_all_tables()

        # 测试无参数
        result = self.processor.group_data_by_column()
        self.assertEqual(len(result), 0, "无参数时应该返回空列表")

        # 测试无效列名
        result = self.processor.group_data_by_column(column_name="不存在的列")
        self.assertEqual(len(result), 0, "无效列名时应该返回空列表")

        # 测试无效列索引
        result = self.processor.group_data_by_column(column_index=999)
        self.assertEqual(len(result), 0, "无效列索引时应该返回空列表")


def run_integration_test():
    """运行集成测试"""
    print("开始运行集成测试...")

    # 测试完整的处理流程
    processor = DiscretionDataProcessor("datarw/data/4675378.docx")

    print("1. 加载文档...")
    if not processor.load_document():
        print("❌ 文档加载失败")
        return False
    print("✅ 文档加载成功")

    print("2. 处理表格...")
    tables_data = processor.process_all_tables()
    if not tables_data:
        print("❌ 表格处理失败")
        return False
    print(f"✅ 成功处理 {len(tables_data)} 个表格")

    print("3. 保存原始 JSON...")
    output_file = "datarw/data/integration_test_output.json"
    if not processor.save_to_json(output_file):
        print("❌ JSON 保存失败")
        return False
    print(f"✅ JSON 保存成功: {output_file}")

    print("4. 生成原始数据摘要...")
    summary = processor.get_summary()
    print("✅ 处理摘要:")
    print(json.dumps(summary, ensure_ascii=False, indent=2))

    print("\n5. 测试数据分组功能...")

    # 测试按列名分组
    print("5.1 按违法行为分组...")
    grouped_data = processor.group_data_by_column(column_name="违法行为")
    if not grouped_data:
        print("❌ 按列名分组失败")
        return False
    print(f"✅ 按违法行为分组成功，共 {grouped_data[0]['分组数量']} 个分组")

    # 保存分组数据
    grouped_output = "datarw/data/integration_test_grouped.json"
    if not processor.save_grouped_data_to_json(grouped_data, grouped_output):
        print("❌ 分组数据保存失败")
        return False
    print(f"✅ 分组数据保存成功: {grouped_output}")

    # 测试按列索引分组
    print("5.2 按裁量阶次分组（列索引4）...")
    penalty_grouped = processor.group_data_by_column(column_index=4)
    if not penalty_grouped:
        print("❌ 按列索引分组失败")
        return False
    print(f"✅ 按裁量阶次分组成功，共 {penalty_grouped[0]['分组数量']} 个分组")

    print("6. 生成分组摘要...")
    grouped_summary = processor.get_grouped_summary(grouped_data)
    print("✅ 分组摘要:")
    print(json.dumps(grouped_summary, ensure_ascii=False, indent=2))

    # 清理测试文件
    test_files = [output_file, grouped_output]
    for file_path in test_files:
        if os.path.exists(file_path):
            os.remove(file_path)

    print("\n🎉 集成测试完成！")
    return True


if __name__ == "__main__":
    print("=" * 50)
    print("自由裁量权数据处理模块测试")
    print("=" * 50)
    
    # 运行单元测试
    print("\n📋 运行单元测试...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    print("\n" + "=" * 50)
    
    # 运行集成测试
    print("\n🔧 运行集成测试...")
    run_integration_test()
